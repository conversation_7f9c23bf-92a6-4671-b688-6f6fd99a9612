import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';
import { campaignAnalyzer } from '@/agents/campaignAnalyzerAgent';
import { WorkflowNames } from '@repo/constants';
import { creatorHashtagScout } from '@/agents/creatorHashtagScout';
import { creatorFilterAgent } from '@/agents/creatorFilterAgent';
import { challengePickerAgent } from '@/agents/challengePickerAgent';
import { workflowNameGenerator } from '@/agents/workflowNameGeneratorAgent';
import { scoutCreaotrWithChallengeWorkflow } from '@/workflows/creatorScoutWorkflow';
import { aiScriptWorkflow } from '@/workflows/aiScriptWorkflow';
import { storage } from '@/lib/memory';

export const mastra = new Mastra({
  agents: {
    campaignAnalyzer,
    creatorHashtagScout,
    creatorFilterAgent,
    challengePickerAgent,
    workflowNameGenerator,
  },
  workflows: {
    [WorkflowNames.aiScriptWorkflow]: aiScriptWorkflow,
    [WorkflowNames.creatorScoutWorkflow]: scout<PERSON>reaotrWithChallengeWorkflow,
  },
  logger: new PinoLogger({
    name: 'JOA_DEV',
    level: 'info',
  }),
  storage,
  telemetry: {
    enabled: false,
  },
});
