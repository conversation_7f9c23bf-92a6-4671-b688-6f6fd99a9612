import { createStep, createWorkflow } from '@mastra/core/workflows';
import { AssistantContent, CoreMessage, CoreUserMessage } from 'ai';
import { z } from 'zod';
import {
  SCOUTING_CONFIG,
  videoScouter,
} from '@/services/scouting/videoScouter';
import { CreatorFilterOutputSchema } from '@/agents/creatorFilterAgent';
import { WorkflowNames } from '@repo/constants';
import { DataContentType, downloadImage } from '@/utils/downloadUtils';
import { ScoutOutputSchema } from '@/agents/creatorHashtagScout';
import { workflowDbService } from '@/services/index';
import { extractFirstJson } from '@/lib/utils';
import { isUrl } from '@/utils/urlUtils';

// Timing utilities
const formatDuration = (ms: number): string => {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
};

const processThumbnailUrl = async (thumbnailUrl: string) => {
  return await downloadImage(thumbnailUrl, DataContentType.UINT8_ARRAY);
};

const createStepSeparator = (
  stepName: string,
  stepId: string,
  action: 'START' | 'END',
  duration?: number,
): string => {
  const separator = '='.repeat(80);
  const timestamp = new Date().toISOString();
  const durationText = duration ? ` (${formatDuration(duration)})` : '';
  return `\n${separator}\n🚀 STEP ${action}: ${stepName.toUpperCase()} [${stepId}]${durationText}\n⏰ ${timestamp}\n${separator}`;
};

// Global workflow timing
let workflowStartTime: number;

// Core workflow input parameters
const workflowInputSchema = z.object({
  targetCreatorDescription: z.string(),
  useIntelligentChallengeSelection: z.boolean().optional().default(false),
  desiredCreatorCount: z.number().optional().default(50),
  filterMode: z.enum(['STRICT', 'LOOSE']),
  pickerMode: z.enum(['STRATEGIC', 'OPPORTUNITY', 'VOLUME']),

  // Video filtering conditions (optional, ignore when value is 0)
  minViews: z.number().optional().default(0),
  minLikes: z.number().optional().default(0),
  minComments: z.number().optional().default(0),
  // Creator filtering conditions (optional, ignore when value is 0)
  minFollowers: z.number().optional().default(0),
  minRecentMedianViews: z.number().optional().default(0),
  minRecentMedianComments: z.number().optional().default(0),
  minRecentMedianLikes: z.number().optional().default(0),
  minRecentAverageViews: z.number().optional().default(0),
  minRecentAverageComments: z.number().optional().default(0),
  minRecentAverageLikes: z.number().optional().default(0),

  // Existing KOL filtering - pass list of unique_ids to exclude from new scouting
  excludeExistingKolIds: z.array(z.string()).optional().default([]),

  // Campaign-specific deduplication parameters (optional)
  campaignMode: z.boolean().optional().default(false),
  batchNumber: z.number().optional(),
  skipVideoIds: z.array(z.string()).optional().default([]),
  skipCreatorIds: z.array(z.string()).optional().default([]),
  sequentialCursorMode: z.boolean().optional().default(false),
  challengeCursorMap: z.record(z.string(), z.number()).optional().default({}),

  // Parallel processing parameters
  concurrentTasksLimit: z.number().min(1).max(10).optional().default(4),

  // Image processing parameters
  uploadToOss: z.boolean().optional().default(false),
  downloadThumbnailAsBuffer: z.boolean().optional().default(false),
});

// Essential pass-through fields for website display only
const sharedParameterSchema = z.object({
  targetCreatorDescription: z.string(),
  desiredCreatorCount: z.number(),
  filterMode: z.enum(['STRICT', 'LOOSE']),
  pickerMode: z.enum(['STRATEGIC', 'OPPORTUNITY', 'VOLUME']),
  scoutGuidance: z.string().optional(),
  creatorRegion: z.string().optional(),
  challengeCursorMap: z.record(z.string(), z.number()).optional(),
  // Parallel processing parameters
  concurrentTasksLimit: z.number().min(1).max(10).optional().default(4),
  // Image processing parameters
  uploadToOss: z.boolean().optional().default(false),
  downloadThumbnailAsBuffer: z.boolean().optional().default(false),
});

// Filter conditions schema for agent analysis
const filterConditionsSchema = z.object({
  minViews: z.number().default(0),
  minLikes: z.number().default(0),
  minComments: z.number().default(0),
  minFollowers: z.number().default(0),
  minRecentMedianViews: z.number().default(0),
  minRecentMedianLikes: z.number().default(0),
  minRecentMedianComments: z.number().default(0),
  minRecentAverageViews: z.number().default(0),
  minRecentAverageLikes: z.number().default(0),
  minRecentAverageComments: z.number().default(0),
  reasoning: z.string().optional(),
});

// Hashtag analysis results with filter conditions and stored filter parameters
const hashtagResultsSchema = z.object({
  core: z.array(z.string()).min(8).max(12),
  reason: z.string().max(80),
  requirementWeights: z.record(z.number()),
  filterConditions: z
    .object({
      minViews: z.number().optional().default(0),
      minLikes: z.number().optional().default(0),
      minComments: z.number().optional().default(0),
      minFollowers: z.number().optional().default(0),
      minRecentMedianViews: z.number().optional().default(0),
      minRecentMedianLikes: z.number().optional().default(0),
      minRecentMedianComments: z.number().optional().default(0),
      minRecentAverageViews: z.number().optional().default(0),
      minRecentAverageLikes: z.number().optional().default(0),
      minRecentAverageComments: z.number().optional().default(0),
      reasoning: z.string().optional(),
    })
    .partial(),
  creatorRegion: z.string().optional(),
  // Store original filter parameters for later steps
  originalFilters: z.object({
    useIntelligentChallengeSelection: z.boolean(),
    minViews: z.number(),
    minLikes: z.number(),
    minComments: z.number(),
    minFollowers: z.number(),
    minRecentMedianViews: z.number(),
    minRecentMedianComments: z.number(),
    minRecentMedianLikes: z.number(),
    minRecentAverageViews: z.number(),
    minRecentAverageComments: z.number(),
    minRecentAverageLikes: z.number(),
  }),
});

// Challenge selection results with original filters and campaign parameters
const challengeResultsSchema = z.object({
  challengeIds: z.array(z.string()),
  keywords: z.array(z.string()),
  // Pass through original filters
  originalFilters: z.object({
    useIntelligentChallengeSelection: z.boolean(),
    minViews: z.number(),
    minLikes: z.number(),
    minComments: z.number(),
    minFollowers: z.number(),
    minRecentMedianViews: z.number(),
    minRecentMedianComments: z.number(),
    minRecentMedianLikes: z.number(),
    minRecentAverageViews: z.number(),
    minRecentAverageComments: z.number(),
    minRecentAverageLikes: z.number(),
  }),
  // Pass through campaign parameters
  campaignParams: z
    .object({
      campaignMode: z.boolean(),
      batchNumber: z.number().optional(),
      skipVideoIds: z.array(z.string()),
      skipCreatorIds: z.array(z.string()),
      sequentialCursorMode: z.boolean(),
      concurrentTasksLimit: z.number().optional(),
    })
    .optional(),
  // Include challenge cursor tracking for campaign progression
  challengeCursorMap: z.record(z.string(), z.number()).optional(),
});

// Creator scouting statistics
const scoutingStatsSchema = z.object({
  challengeVideosCollected: z.number(),
  uniqueCreatorsFound: z.number(),
  creatorsProcessed: z.number(),
  successfulCreators: z.number(),
  totalCreatorVideos: z.number(),
  totalVideosProcessed: z.number(),
});

// Creator and video data with original filters and campaign parameters
const creatorDataSchema = z.object({
  creators: z.array(z.any()), // Enhanced creator data with videoCount
  videos: z.array(z.any()), // Creator videos (optimized)
  stats: scoutingStatsSchema,
  // Pass through original filters
  originalFilters: z.object({
    useIntelligentChallengeSelection: z.boolean(),
    minViews: z.number(),
    minLikes: z.number(),
    minComments: z.number(),
    minFollowers: z.number(),
    minRecentMedianViews: z.number(),
    minRecentMedianComments: z.number(),
    minRecentMedianLikes: z.number(),
    minRecentAverageViews: z.number(),
    minRecentAverageComments: z.number(),
    minRecentAverageLikes: z.number(),
  }),
  // Pass through campaign parameters
  campaignParams: z
    .object({
      campaignMode: z.boolean(),
      batchNumber: z.number().optional(),
      skipVideoIds: z.array(z.string()),
      skipCreatorIds: z.array(z.string()),
      sequentialCursorMode: z.boolean(),
      concurrentTasksLimit: z.number().optional(),
    })
    .optional(),
  // Include challenge cursor tracking for campaign progression
  challengeCursorMap: z.record(z.string(), z.number()).optional(),
});

/**
 * Extract essential video data for filtering (reduce payload size)
 * @param video Full video object
 * @returns Minimal video data for context
 */
function extractVideoEssentials(video: any) {
  return {
    video_id: video.video_id,
    description: video.description, // Contains title and hashtags
    view_count: video.view_count,
    like_count: video.like_count,
    comment_count: video.comment_count,
    share_count: video.share_count,
    thumbnail_url: video.thumbnail_url,
  };
}

/**
 * Calculate average value from an array of numbers
 * @param values Array of numbers
 * @returns Average value
 */
function calculateAverage(values: number[]): number {
  if (values.length === 0) return 0;
  return values.reduce((sum, val) => sum + val, 0) / values.length;
}

/**
 * Calculate median value from an array of numbers
 * @param values Array of numbers
 * @returns Median value
 */
function calculateMedian(values: number[]): number {
  if (values.length === 0) return 0;

  const sorted = [...values].sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);

  if (sorted.length % 2 === 0) {
    return (sorted[mid - 1] + sorted[mid]) / 2;
  }
  return sorted[mid];
}

/**
 * Calculate enhanced metrics for creator videos with error handling
 * @param videos Array of video objects
 * @returns Enhanced metrics object
 */
function calculateVideoMetrics(videos: any[]) {
  try {
    // Validate input
    if (!Array.isArray(videos) || videos.length === 0) {
      return {
        recentVideosCollected: 0,
        averageViews: 0,
        averageLikes: 0,
        averageComments: 0,
        medianViews: 0,
        medianLikes: 0,
        medianComments: 0,
        avgEngagementRate: 0,
        totalViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
      };
    }

    // Filter out invalid video objects and extract metrics safely
    const validVideos = videos.filter((v) => v && typeof v === 'object');
    if (validVideos.length === 0) {
      console.warn('⚠️ No valid video objects found for metrics calculation');
      return {
        recentVideosCollected: 0,
        averageViews: 0,
        averageLikes: 0,
        averageComments: 0,
        medianViews: 0,
        medianLikes: 0,
        medianComments: 0,
        avgEngagementRate: 0,
        totalViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
      };
    }

    const views = validVideos.map((v) =>
      Math.max(0, Number(v.view_count) || 0),
    );
    const likes = validVideos.map((v) =>
      Math.max(0, Number(v.like_count) || 0),
    );
    const comments = validVideos.map((v) =>
      Math.max(0, Number(v.comment_count) || 0),
    );
    const shares = validVideos.map((v) =>
      Math.max(0, Number(v.share_count) || 0),
    );

    const totalViews = views.reduce((sum, val) => sum + val, 0);
    const totalLikes = likes.reduce((sum, val) => sum + val, 0);
    const totalComments = comments.reduce((sum, val) => sum + val, 0);
    const totalShares = shares.reduce((sum, val) => sum + val, 0);

    // Calculate engagement rate (likes + comments + shares) / views with safe division
    const engagementRates = validVideos.map((v) => {
      const totalEngagement =
        Math.max(0, Number(v.like_count) || 0) +
        Math.max(0, Number(v.comment_count) || 0) +
        Math.max(0, Number(v.share_count) || 0);
      const viewCount = Math.max(0, Number(v.view_count) || 0);
      return viewCount > 0 ? totalEngagement / viewCount : 0;
    });

    const avgEngagementRate =
      engagementRates.length > 0
        ? engagementRates.reduce((sum, rate) => sum + rate, 0) /
          engagementRates.length
        : 0;

    return {
      recentVideosCollected: validVideos.length,
      averageViews: Math.round(calculateAverage(views)),
      averageLikes: Math.round(calculateAverage(likes)),
      averageComments: Math.round(calculateAverage(comments)),
      medianViews: calculateMedian(views),
      medianLikes: calculateMedian(likes),
      medianComments: calculateMedian(comments),
      avgEngagementRate: Math.round(avgEngagementRate * 10000) / 100, // Convert to percentage with 2 decimal places
      totalViews,
      totalLikes,
      totalComments,
      totalShares,
    };
  } catch (error) {
    console.error('❌ Error calculating video metrics:', error);
    // Return safe default values
    return {
      recentVideosCollected: 0,
      averageViews: 0,
      averageLikes: 0,
      averageComments: 0,
      medianViews: 0,
      medianLikes: 0,
      medianComments: 0,
      avgEngagementRate: 0,
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
    };
  }
}

// Schema for the hashtag search results with filter conditions
const searchHashtagSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  reason: z.string().optional(),
  filterConditions: filterConditionsSchema.optional(),
});

// Enhanced creator metrics schema for rich results
const creatorMetricsSchema = z.object({
  // Standard creator metrics
  unique_id: z.string(),
  nickname: z.string(),
  follower_count: z.number().optional(),
  aweme_count: z.number().optional(), // Post count
  create_time: z.number().optional(), // Account creation timestamp
  region: z.string().optional(),
  language: z.string().optional(),

  // Advanced video metrics
  recentVideosCollected: z.number(),
  averageViews: z.number(),
  averageLikes: z.number(),
  averageComments: z.number(),
  medianViews: z.number(),
  medianLikes: z.number(),
  medianComments: z.number(),
  avgEngagementRate: z.number(), // Percentage
});

// Simplified workflow output schema - only essential data/IDs
const enhancedWorkflowOutputSchema = z.object({
  scoutedCreators: z.number(),
  qualifiedCreators: z.number(),
  contextId: z.string(), // ID to retrieve full results from database
  filterSummary: z
    .object({
      mode: z.enum(['STRICT', 'LOOSE']),
      total_analyzed: z.number(),
      total_qualified: z.number(),
      tier_breakdown: z.object({
        PERFECT: z.number(),
        EXCELLENT: z.number(),
        GOOD: z.number(),
        ACCEPTABLE: z.number(),
      }),
    })
    .optional(),
});

export const creatorScoutWorkflow = createWorkflow({
  id: WorkflowNames.creatorScoutWorkflow,
  inputSchema: workflowInputSchema,
  outputSchema: enhancedWorkflowOutputSchema,
});

export const creatorDirectScoutWorkflow = createWorkflow({
  id: WorkflowNames.creatorDirectScoutWorkflow,
  inputSchema: workflowInputSchema,
  outputSchema: enhancedWorkflowOutputSchema,
});

// Step 1: Analyze the user's requirements and generate hashtags
const analyzeRequirementStep = createStep({
  id: 'analyzeRequirement',
  inputSchema: workflowInputSchema,
  resumeSchema: z.object({
    userInputMessage: z.string().optional(),
    messages: z.array(z.any()),
  }),
  outputSchema: hashtagResultsSchema.merge(sharedParameterSchema),
  execute: async ({ inputData, resumeData, suspend, mastra }) => {
    // Initialize workflow timing on first step
    if (!workflowStartTime) {
      workflowStartTime = Date.now();
    }

    const stepStartTime = Date.now();
    console.log(
      createStepSeparator(
        'Analyze Requirements',
        'analyzeRequirement',
        'START',
      ),
    );
    console.log('📋 Analyzing user requirements and generating hashtags...');
    console.log('🎯 Target:', inputData.targetCreatorDescription);
    console.log('🔧 Filter Mode:', inputData.filterMode);
    console.log('🎯 Picker Mode:', inputData.pickerMode);
    console.log('📊 Desired Count:', inputData.desiredCreatorCount);
    // console.log('inputData', inputData);
    // console.log('resumeData', resumeData);

    const scoutAgent = mastra?.getAgent('creatorHashtagScout');
    if (!scoutAgent) {
      throw new Error('Campaign analyzer agent not found');
    }

    // Initialize messages array
    let messages: CoreMessage[] = [];

    // Check if this is a resume (either resumeData exists OR inputData has been contaminated with resume data)
    const isResume = resumeData?.messages || (inputData as any).messages;

    if (isResume) {
      // If we have cached messages, use them (this is a resume)
      // console.log('Found cached messages, resuming conversation');

      // Get messages from resumeData first, fallback to inputData if framework merged them
      const cachedMessages =
        resumeData?.messages || (inputData as any).messages;
      messages = [...cachedMessages] as CoreMessage[]; // Create a copy to avoid mutation

      // Get user input message from resumeData first, fallback to inputData
      const userInputMessage =
        resumeData?.userInputMessage || (inputData as any).userInputMessage;

      // Add the user's response from the resumed workflow
      if (userInputMessage) {
        // console.log('user responded:', userInputMessage);
        const userResponse: CoreMessage = {
          role: 'user',
          content: userInputMessage,
        };
        messages.push(userResponse);
      }
    } else {
      // If no cached messages, start a new conversation
      // console.log('No cached messages found, starting new conversation');
      const description = inputData.targetCreatorDescription;
      const userDescription: CoreMessage = {
        role: 'user',
        content: description,
      };
      messages.push(userDescription);
    }

    // console.log('About to call agent with messages:', messages.length);
    // Generate a response from the agent
    // This should be a creative work, make the temperature higher
    const resp = await scoutAgent.generate(messages, {
      temperature: 1,
    });

    const assistantMessage = resp.response;
    const assistantContent = resp.response.messages[0]
      .content as AssistantContent;

    // We knew that assitant content is not string, Find first TextPart in assistantContent
    let content: string;
    if (typeof assistantContent === 'string') {
      content = assistantContent;
    } else {
      content =
        assistantContent.find((part) => part.type === 'text')?.text ||
        'No content responsed!';
    }

    let object;
    try {
      object = extractFirstJson(content);
    } catch {
      console.warn('Failed to parse agent response as JSON:', content);
      throw new Error('Invalid response format from agent');
    }

    // const content = JSON.stringify(object);

    const parsedResult = ScoutOutputSchema.safeParse(object);
    if (parsedResult.success) {
      const stepDuration = Date.now() - stepStartTime;
      console.log('✅ Successfully generated hashtags:', parsedResult.data);
      console.log(
        createStepSeparator(
          'Analyze Requirements',
          'analyzeRequirement',
          'END',
          stepDuration,
        ),
      );

      // Use filter conditions from agent analysis if available, otherwise fall back to input parameters
      const agentFilters = parsedResult.data.filterConditions;
      const effectiveFilters = {
        minViews: agentFilters?.minViews ?? inputData.minViews ?? 0,
        minLikes: agentFilters?.minLikes ?? inputData.minLikes ?? 0,
        minComments: agentFilters?.minComments ?? inputData.minComments ?? 0,
        minFollowers: agentFilters?.minFollowers ?? inputData.minFollowers ?? 0,
        minRecentMedianViews:
          agentFilters?.minRecentMedianViews ??
          inputData.minRecentMedianViews ??
          0,
        minRecentMedianComments:
          agentFilters?.minRecentMedianComments ??
          inputData.minRecentMedianComments ??
          0,
        minRecentMedianLikes:
          agentFilters?.minRecentMedianLikes ??
          inputData.minRecentMedianLikes ??
          0,
        minRecentAverageViews:
          agentFilters?.minRecentAverageViews ??
          inputData.minRecentAverageViews ??
          0,
        minRecentAverageComments:
          agentFilters?.minRecentAverageComments ??
          inputData.minRecentAverageComments ??
          0,
        minRecentAverageLikes:
          agentFilters?.minRecentAverageLikes ??
          inputData.minRecentAverageLikes ??
          0,
      };

      // Log filter conditions analysis
      if (agentFilters) {
        console.log('🎯 Agent analyzed filter conditions:');
        console.log(
          `   📊 Min Followers: ${effectiveFilters.minFollowers.toLocaleString()}`,
        );
        console.log(
          `   👀 Min Views: ${effectiveFilters.minViews.toLocaleString()}`,
        );
        console.log(
          `   💖 Min Likes: ${effectiveFilters.minLikes.toLocaleString()}`,
        );
        console.log(
          `   💬 Min Comments: ${effectiveFilters.minComments.toLocaleString()}`,
        );
        console.log(
          `   📈 Min Median Views: ${effectiveFilters.minRecentMedianViews.toLocaleString()}`,
        );
        console.log(
          `   📈 Min Median Likes: ${effectiveFilters.minRecentMedianLikes.toLocaleString()}`,
        );
        console.log(
          `   📈 Min Median Comments: ${effectiveFilters.minRecentMedianComments.toLocaleString()}`,
        );
        console.log(
          `   📊 Min Average Views: ${effectiveFilters.minRecentAverageViews.toLocaleString()}`,
        );
        console.log(
          `   📊 Min Average Likes: ${effectiveFilters.minRecentAverageLikes.toLocaleString()}`,
        );
        console.log(
          `   📊 Min Average Comments: ${effectiveFilters.minRecentAverageComments.toLocaleString()}`,
        );
        if (agentFilters.reasoning) {
          console.log(`   💡 Reasoning: ${agentFilters.reasoning}`);
        }
      } else {
        console.log(
          '🔧 Using input filter parameters (no agent analysis available)',
        );
      }

      // console.log('parseResult.data', parseResult.data);
      return {
        ...parsedResult.data,
        targetCreatorDescription: inputData.targetCreatorDescription,
        desiredCreatorCount: inputData.desiredCreatorCount ?? 50,
        filterMode: inputData.filterMode ?? 'LOOSE',
        pickerMode: inputData.pickerMode ?? 'STRATEGIC',
        scoutGuidance: content, // Use full response as guidance
        creatorRegion: parsedResult.data.creatorRegion, // Pass through extracted region
        // Pass through parallel processing parameters
        concurrentTasksLimit: inputData.concurrentTasksLimit ?? 4,
        // Pass through challenge cursor tracking
        challengeCursorMap: inputData.challengeCursorMap ?? {},
        // Pass through image processing parameters
        uploadToOss: inputData.uploadToOss ?? false,
        downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
        // Store original filter parameters for later steps
        originalFilters: {
          useIntelligentChallengeSelection:
            inputData.useIntelligentChallengeSelection ?? false,
          minViews: effectiveFilters.minViews,
          minLikes: effectiveFilters.minLikes,
          minComments: effectiveFilters.minComments,
          minFollowers: effectiveFilters.minFollowers,
          minRecentMedianViews: effectiveFilters.minRecentMedianViews,
          minRecentMedianComments: effectiveFilters.minRecentMedianComments,
          minRecentMedianLikes: effectiveFilters.minRecentMedianLikes,
          minRecentAverageViews: effectiveFilters.minRecentAverageViews,
          minRecentAverageComments: effectiveFilters.minRecentAverageComments,
          minRecentAverageLikes: effectiveFilters.minRecentAverageLikes,
        },
        // Store campaign parameters for later steps
        campaignParams: inputData.campaignMode
          ? {
              campaignMode: inputData.campaignMode,
              batchNumber: inputData.batchNumber,
              skipVideoIds: inputData.skipVideoIds ?? [],
              skipCreatorIds: inputData.skipCreatorIds ?? [],
              sequentialCursorMode: inputData.sequentialCursorMode ?? false,
            }
          : undefined,
      };
    }

    // If not in expected format, add assistant message to conversation and suspend
    const updatedMessages = [...messages, assistantMessage];
    // console.log('appended messages', messages);
    // console.log('updatedMessages', updatedMessages);

    // console.log('before suspend, we are here');

    // Suspend and wait for user input
    // When resumed, the step will restart with the updated messages in resumeData
    await suspend({
      messages: updatedMessages,
      message: content,
    });

    // console.log('after suspend, we are here, returning empty hashtags');

    // This code should not execute in normal operation since suspend should restart the step
    // But we need to return something to satisfy TypeScript
    return {
      core: [],
      reason: '',
      requirementWeights: {},
      filterConditions: {},
      targetCreatorDescription: inputData.targetCreatorDescription,
      desiredCreatorCount: inputData.desiredCreatorCount ?? 50,
      filterMode: inputData.filterMode,
      pickerMode: inputData.pickerMode,
      scoutGuidance: 'No guidance available',
      creatorRegion: undefined, // No region extracted in fallback case
      // Pass through parallel processing parameters
      concurrentTasksLimit: inputData.concurrentTasksLimit ?? 4,
      // Pass through challenge cursor tracking
      challengeCursorMap: inputData.challengeCursorMap ?? {},
      // Pass through image processing parameters
      uploadToOss: inputData.uploadToOss ?? false,
      downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
      // Store original filter parameters for later steps
      originalFilters: {
        useIntelligentChallengeSelection:
          inputData.useIntelligentChallengeSelection ?? false,
        minViews: inputData.minViews ?? 0,
        minLikes: inputData.minLikes ?? 0,
        minComments: inputData.minComments ?? 0,
        minFollowers: inputData.minFollowers ?? 0,
        minRecentMedianViews: inputData.minRecentMedianViews ?? 0,
        minRecentMedianComments: inputData.minRecentMedianComments ?? 0,
        minRecentMedianLikes: inputData.minRecentMedianLikes ?? 0,
        minRecentAverageViews: inputData.minRecentAverageViews ?? 0,
        minRecentAverageComments: inputData.minRecentAverageComments ?? 0,
        minRecentAverageLikes: inputData.minRecentAverageLikes ?? 0,
      },
    };
  },
});

// Path 1.1: Select challenges from keywords
const selectChallengesStep = createStep({
  id: 'select-challenges',
  inputSchema: hashtagResultsSchema.merge(sharedParameterSchema).extend({
    campaignParams: z
      .object({
        campaignMode: z.boolean(),
        batchNumber: z.number().optional(),
        skipVideoIds: z.array(z.string()),
        skipCreatorIds: z.array(z.string()),
        sequentialCursorMode: z.boolean(),
        concurrentTasksLimit: z.number().optional(),
      })
      .optional(),
  }),
  outputSchema: challengeResultsSchema.merge(sharedParameterSchema),
  execute: async ({ inputData, mastra }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Select Challenges', 'select-challenges', 'START'),
    );

    const {
      core,
      targetCreatorDescription,
      desiredCreatorCount,
      filterMode,
      pickerMode,
      scoutGuidance,
      originalFilters,
      campaignParams,
    } = inputData;

    const useIntelligentChallengeSelection =
      originalFilters.useIntelligentChallengeSelection;

    const allKeywords = [...core];
    console.log('🔍 Selecting challenges from keywords:', allKeywords);
    // console.log(
    //   '🧠 Using intelligent selection:',
    //   useIntelligentChallengeSelection,
    // );
    // console.log('🎯 Target creator count:', desiredCreatorCount);

    const challengePickerAgent = mastra?.getAgent('challengePickerAgent');
    if (!challengePickerAgent) {
      throw new Error('Challenge picker agent not found');
    }

    const challengeIds = await videoScouter.selectChallengesFromKeywords(
      allKeywords,
      {
        useIntelligentSelection: useIntelligentChallengeSelection,
        targetCreatorDescription,
        scoutGuidance,
        agent: challengePickerAgent,
        pickerMode,
      },
    );

    const stepDuration = Date.now() - stepStartTime;
    console.log(
      `✅ Selected ${challengeIds.length} challenges for creator scouting`,
    );
    console.log(
      createStepSeparator(
        'Select Challenges',
        'select-challenges',
        'END',
        stepDuration,
      ),
    );

    return {
      challengeIds,
      keywords: allKeywords,
      targetCreatorDescription,
      desiredCreatorCount,
      filterMode,
      pickerMode,
      scoutGuidance,
      // Pass through parallel processing parameters
      concurrentTasksLimit: inputData.concurrentTasksLimit ?? 4,
      // Pass through challenge cursor tracking
      challengeCursorMap: inputData.challengeCursorMap ?? {},
      // Pass through image processing parameters
      uploadToOss: inputData.uploadToOss ?? false,
      downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
      originalFilters,
      campaignParams,
    };
  },
});

// Path 1.2: Scout creators from challenges
const scoutCreatorsStep = createStep({
  id: 'scout-creators',
  inputSchema: challengeResultsSchema.merge(sharedParameterSchema),
  outputSchema: creatorDataSchema.merge(
    sharedParameterSchema.pick({
      targetCreatorDescription: true,
      filterMode: true,
      scoutGuidance: true,
      creatorRegion: true,
      desiredCreatorCount: true,
    }),
  ),
  execute: async ({ inputData }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Scout Creators', 'scout-creators', 'START'),
    );

    const {
      challengeIds,
      desiredCreatorCount,
      targetCreatorDescription,
      filterMode,
      scoutGuidance,
      creatorRegion,
      originalFilters,
      campaignParams,
    } = inputData;

    // Extract filter parameters from stored original filters
    const {
      minViews,
      minLikes,
      minComments,
      minFollowers,
      minRecentMedianViews,
      minRecentMedianComments,
      minRecentMedianLikes,
      minRecentAverageViews,
      minRecentAverageComments,
      minRecentAverageLikes,
    } = originalFilters;

    // Extract campaign parameters for deduplication
    const skipVideoIds = campaignParams?.skipVideoIds ?? [];
    const skipCreatorIds = campaignParams?.skipCreatorIds ?? [];
    const sequentialCursorMode = campaignParams?.sequentialCursorMode ?? false;
    const batchNumber = campaignParams?.batchNumber;
    const campaignMode = campaignParams?.campaignMode ?? false;

    // Extract cursor tracking parameters
    const challengeCursorMap = inputData.challengeCursorMap ?? {};

    // Extract parallel processing parameters
    const concurrentTasksLimit = inputData.concurrentTasksLimit;

    console.log(`🕵️ Scouting creators from ${challengeIds.length} challenges`);
    console.log(`⚡ Concurrent tasks limit: ${concurrentTasksLimit}`);
    if (creatorRegion) {
      console.log(`🌍 Target region: ${creatorRegion}`);
    }
    if (campaignMode) {
      console.log(`🎯 Campaign mode: Batch ${batchNumber || 'N/A'}`);
      // console.log(`🚫 Skipping ${skipVideoIds.length} already scouted videos`);
      console.log(
        `🚫 Skipping ${skipCreatorIds.length} already scouted creators`,
      );
      console.log(
        `� Sequential cursor mode: ${sequentialCursorMode ? 'ON' : 'OFF'}`,
      );
    }

    // Log active filters
    const activeFilters = [];
    if (minViews > 0)
      activeFilters.push(`minViews: ${minViews.toLocaleString()}`);
    if (minLikes > 0)
      activeFilters.push(`minLikes: ${minLikes.toLocaleString()}`);
    if (minComments > 0)
      activeFilters.push(`minComments: ${minComments.toLocaleString()}`);
    if (minFollowers > 0)
      activeFilters.push(`minFollowers: ${minFollowers.toLocaleString()}`);
    if (minRecentMedianViews > 0)
      activeFilters.push(
        `minRecentMedianViews: ${minRecentMedianViews.toLocaleString()}`,
      );
    if (minRecentMedianComments > 0)
      activeFilters.push(
        `minRecentMedianComments: ${minRecentMedianComments.toLocaleString()}`,
      );
    if (minRecentMedianLikes > 0)
      activeFilters.push(
        `minRecentMedianLikes: ${minRecentMedianLikes.toLocaleString()}`,
      );
    if (minRecentAverageViews > 0)
      activeFilters.push(
        `minRecentAverageViews: ${minRecentAverageViews.toLocaleString()}`,
      );
    if (minRecentAverageComments > 0)
      activeFilters.push(
        `minRecentAverageComments: ${minRecentAverageComments.toLocaleString()}`,
      );
    if (minRecentAverageLikes > 0)
      activeFilters.push(
        `minRecentAverageLikes: ${minRecentAverageLikes.toLocaleString()}`,
      );

    if (activeFilters.length > 0) {
      console.log(`🔍 Active filters: ${activeFilters.join(', ')}`);
    } else {
      console.log('🔍 No filters applied (all values are 0)');
    }

    if (challengeIds.length === 0) {
      const stepDuration = Date.now() - stepStartTime;
      console.warn('⚠️  No challenges selected, returning empty results');
      console.log(
        createStepSeparator(
          'Scout Creators',
          'scout-creators',
          'END',
          stepDuration,
        ),
      );
      return {
        creators: [],
        videos: [],
        stats: {
          challengeVideosCollected: 0,
          uniqueCreatorsFound: 0,
          creatorsProcessed: 0,
          successfulCreators: 0,
          totalCreatorVideos: 0,
          totalVideosProcessed: 0,
        },
        targetCreatorDescription,
        filterMode,
        scoutGuidance,
        desiredCreatorCount,
        originalFilters,
      };
    }

    // Scout creators from each challenge and combine results
    const allCreators: any[] = [];
    const allVideos: any[] = [];
    let totalStats = {
      challengeVideosCollected: 0,
      uniqueCreatorsFound: 0,
      creatorsProcessed: 0,
      successfulCreators: 0,
      totalCreatorVideos: 0,
      totalVideosProcessed: 0,
    };

    // Calculate creators per challenge to reach desired total
    const creatorsPerChallenge = Math.max(
      Math.ceil(desiredCreatorCount / challengeIds.length),
      SCOUTING_CONFIG.MIN_CREATOR_PER_CHALLENGE,
    );
    // const creatorsPerChallenge = desiredCreatorCount;

    console.log(
      `🚀 Processing ${challengeIds.length} challenges in parallel (max ${SCOUTING_CONFIG.MAX_CONCURRENT_JOBS} concurrent)`,
    );

    // Create tasks for parallel processing
    const challengeTasks = challengeIds.map((challengeId, idx) => async () => {
      // Retry logic for scouting challenge creators
      let retryCount = 0;
      const maxRetries = 3;
      let lastError: any;

      while (retryCount < maxRetries) {
        try {
          if (retryCount > 0) {
            console.log(
              `🔄 Retrying challenge ${challengeId} (attempt ${retryCount + 1}/${maxRetries}) [${idx + 1}/${challengeIds.length}]`,
            );
          }

          // Determine cursor strategy based on campaign mode and stored progress
          let startCursor: number;
          if (campaignMode && challengeCursorMap[challengeId] !== undefined) {
            // For campaigns: use stored cursor to continue from where we left off
            startCursor = challengeCursorMap[challengeId];
            console.log(
              `🔄 Campaign mode: Challenge ${challengeId} resuming from stored cursor: ${startCursor} [${idx + 1}/${challengeIds.length}]`,
            );
          } else if (sequentialCursorMode) {
            // For campaigns: sequential processing starting from 0
            // Each challenge gets a different starting point to avoid overlap
            startCursor = idx * 25; // Spread challenges across cursor positions
            console.log(
              `📊 Campaign mode: Challenge ${challengeId} using sequential cursor: ${startCursor} [${idx + 1}/${challengeIds.length}]`,
            );
          } else {
            // For single workflows: random cursor for variety
            startCursor = Math.floor(
              (Math.random() * SCOUTING_CONFIG.MAX_CURSOR_LIMIT) / 2,
            ); // 0 to Max Limit
            console.log(
              `🎲 Single mode: Challenge ${challengeId} using random cursor: ${startCursor} [${idx + 1}/${challengeIds.length}]`,
            );
          }

          const result = await videoScouter.scoutChallengeCreators(
            challengeId,
            creatorsPerChallenge,
            {
              minViews,
              minLikes,
              minComments,
              minFollowers,
              minRecentMedianViews,
              minRecentMedianComments,
              minRecentMedianLikes,
              minRecentAverageViews,
              minRecentAverageComments,
              minRecentAverageLikes,
            },
            skipVideoIds, // Pass campaign skip list for video deduplication
            startCursor, // Use campaign-aware cursor strategy
            true, // skipOssUpload
            skipCreatorIds, // Pass campaign skip list for creator deduplication
            concurrentTasksLimit, // Use configured concurrent tasks limit
            creatorRegion, // Pass extracted region for regional video filtering
          );

          console.log(
            `✅ Challenge ${challengeId} yielded ${result.creators.length} creators (cursor: ${startCursor}→${result.stats.finalCursor}) [${idx + 1}/${challengeIds.length}]${retryCount > 0 ? ` (succeeded on retry ${retryCount + 1})` : ''}`,
          );

          return {
            challengeId,
            idx,
            result,
          };
        } catch (error) {
          retryCount++;
          lastError = error;
          console.error(
            `❌ Error scouting challenge ${challengeId} (attempt ${retryCount}/${maxRetries}):`,
            error,
          );

          if (retryCount >= maxRetries) {
            console.error(
              `🚫 Failed to scout challenge ${challengeId} after ${maxRetries} attempts. Skipping challenge.`,
            );
            // Return empty result instead of throwing to allow other challenges to continue
            return {
              challengeId,
              idx,
              result: {
                creators: [],
                videos: [],
                stats: {
                  challengeVideosCollected: 0,
                  uniqueCreatorsFound: 0,
                  creatorsProcessed: 0,
                  successfulCreators: 0,
                  totalCreatorVideos: 0,
                  totalVideosProcessed: 0,
                },
              },
            };
          } else {
            // Wait before retrying (exponential backoff)
            const waitTime = Math.pow(2, retryCount - 1) * 1000; // 1s, 2s, 4s
            console.log(`⏳ Waiting ${waitTime}ms before retry...`);
            await new Promise((resolve) => setTimeout(resolve, waitTime));
          }
        }
      }

      // This should never be reached, but TypeScript needs a return
      throw lastError;
    });

    // Execute challenges in parallel with controlled concurrency
    // Since processInParallel is private, we'll implement the parallel processing here
    const challengeResults: PromiseSettledResult<{
      challengeId: string;
      idx: number;
      result: any;
    }>[] = [];

    const maxConcurrency = SCOUTING_CONFIG.MAX_CONCURRENT_JOBS;
    for (let i = 0; i < challengeTasks.length; i += maxConcurrency) {
      const batch = challengeTasks.slice(i, i + maxConcurrency);
      const batchResults = await Promise.allSettled(
        batch.map((task) => task()),
      );
      challengeResults.push(...batchResults);

      // Log progress
      console.log(
        `🔄 Processed batch ${Math.floor(i / maxConcurrency) + 1}/${Math.ceil(challengeTasks.length / maxConcurrency)} (${batchResults.length} challenges)`,
      );
    }

    // Process results and accumulate data
    for (const challengeResult of challengeResults) {
      if (challengeResult.status === 'fulfilled') {
        const { result } = challengeResult.value;

        allCreators.push(...result.creators);
        allVideos.push(...result.videos);

        // Accumulate stats
        totalStats.challengeVideosCollected +=
          result.stats.challengeVideosCollected;
        totalStats.uniqueCreatorsFound += result.stats.uniqueCreatorsFound;
        totalStats.creatorsProcessed += result.stats.creatorsProcessed;
        totalStats.successfulCreators += result.stats.successfulCreators;
        totalStats.totalCreatorVideos += result.stats.totalCreatorVideos;
        totalStats.totalVideosProcessed += result.stats.totalVideosProcessed;

        // console.log(`📊 Accumulated data from challenge ${challengeId}`);
      } else {
        console.error(`Failed challenge processing:`, challengeResult.reason);
      }
    }

    console.log(
      `🎯 Parallel processing complete: ${allCreators.length} total creators, ${allVideos.length} total videos`,
    );

    // Deduplicate creators by unique_id and filter out campaign-scouted creators
    const creatorMap = new Map();
    const uniqueCreators = [];

    for (const creator of allCreators) {
      const creatorId = creator.unique_id;

      // Skip if already processed in this workflow run
      if (creatorMap.has(creatorId)) {
        continue;
      }

      // Skip if already scouted in campaign (campaign-aware deduplication)
      if (campaignMode && skipCreatorIds.includes(creatorId)) {
        continue;
      }

      creatorMap.set(creatorId, creator);
      uniqueCreators.push(creator);
    }

    // Log deduplication results
    const totalCreators = allCreators.length;
    const duplicatesFiltered = totalCreators - uniqueCreators.length;
    if (campaignMode && duplicatesFiltered > 0) {
      console.log(
        `🔄 Campaign deduplication: ${duplicatesFiltered}/${totalCreators} creators filtered (${uniqueCreators.length} unique)`,
      );
    }

    const stepDuration = Date.now() - stepStartTime;
    console.log(
      `✅ Final results: ${uniqueCreators.length} unique creators, ${allVideos.length} videos`,
    );
    console.log(
      createStepSeparator(
        'Scout Creators',
        'scout-creators',
        'END',
        stepDuration,
      ),
    );

    return {
      creators: uniqueCreators,
      videos: allVideos,
      stats: totalStats,
      targetCreatorDescription,
      filterMode,
      scoutGuidance,
      creatorRegion,
      desiredCreatorCount,
      // Pass through image processing parameters
      uploadToOss: inputData.uploadToOss ?? false,
      downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
      originalFilters,
      campaignParams,
      // Include challenge cursor map for campaign progression tracking
      challengeCursorMap: campaignMode
        ? Object.fromEntries(
            challengeResults
              .filter(
                (cr: PromiseSettledResult<any>) => cr.status === 'fulfilled',
              )
              .map((cr: PromiseFulfilledResult<any>) => [
                cr.value.challengeId,
                cr.value.result.stats.finalCursor,
              ]),
          )
        : {},
    };
  },
});

// Step 2.1: Direct video scouting (optional)
const directVideoScoutStep = createStep({
  id: 'direct-video-scout',
  inputSchema: hashtagResultsSchema.merge(sharedParameterSchema).extend({
    campaignParams: z
      .object({
        campaignMode: z.boolean(),
        batchNumber: z.number().optional(),
        skipVideoIds: z.array(z.string()),
        skipCreatorIds: z.array(z.string()),
        sequentialCursorMode: z.boolean(),
        concurrentTasksLimit: z.number().optional(),
      })
      .optional(),
  }),
  outputSchema: creatorDataSchema.merge(sharedParameterSchema),
  execute: async ({ inputData }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Direct Video Scout', 'direct-video-scout', 'START'),
    );

    const {
      core,
      targetCreatorDescription,
      desiredCreatorCount,
      filterMode,
      pickerMode,
      scoutGuidance,
      creatorRegion,
      originalFilters,
      campaignParams,
    } = inputData;

    // Extract filter parameters from stored original filters
    const {
      minViews,
      minLikes,
      minComments,
      minFollowers,
      minRecentMedianViews,
      minRecentMedianComments,
      minRecentMedianLikes,
      minRecentAverageViews,
      minRecentAverageComments,
      minRecentAverageLikes,
    } = originalFilters;

    // Extract campaign parameters for deduplication
    const skipVideoIds = campaignParams?.skipVideoIds ?? [];
    const skipCreatorIds = campaignParams?.skipCreatorIds ?? [];
    const sequentialCursorMode = campaignParams?.sequentialCursorMode ?? false;
    const batchNumber = campaignParams?.batchNumber;
    const campaignMode = campaignParams?.campaignMode ?? false;

    // Extract parallel processing parameters
    const concurrentTasksLimit = inputData.concurrentTasksLimit;

    const allKeywords = [...core];
    console.log(
      `🔍 Direct video scouting using keywords: ${allKeywords.join(', ')}`,
    );
    console.log(`⚡ Concurrent tasks limit: ${concurrentTasksLimit}`);
    if (creatorRegion) {
      console.log(`🌍 Target region: ${creatorRegion}`);
    }
    if (campaignMode) {
      console.log(`🎯 Campaign mode: Batch ${batchNumber || 'N/A'}`);
      console.log(
        `🚫 Skipping ${skipCreatorIds.length} already scouted creators`,
      );
    }

    // Log active filters
    const activeFilters = [];
    if (minViews > 0)
      activeFilters.push(`minViews: ${minViews.toLocaleString()}`);
    if (minLikes > 0)
      activeFilters.push(`minLikes: ${minLikes.toLocaleString()}`);
    if (minComments > 0)
      activeFilters.push(`minComments: ${minComments.toLocaleString()}`);
    if (minFollowers > 0)
      activeFilters.push(`minFollowers: ${minFollowers.toLocaleString()}`);
    if (minRecentMedianViews > 0)
      activeFilters.push(
        `minRecentMedianViews: ${minRecentMedianViews.toLocaleString()}`,
      );
    if (minRecentMedianComments > 0)
      activeFilters.push(
        `minRecentMedianComments: ${minRecentMedianComments.toLocaleString()}`,
      );
    if (minRecentMedianLikes > 0)
      activeFilters.push(
        `minRecentMedianLikes: ${minRecentMedianLikes.toLocaleString()}`,
      );
    if (minRecentAverageViews > 0)
      activeFilters.push(
        `minRecentAverageViews: ${minRecentAverageViews.toLocaleString()}`,
      );
    if (minRecentAverageComments > 0)
      activeFilters.push(
        `minRecentAverageComments: ${minRecentAverageComments.toLocaleString()}`,
      );
    if (minRecentAverageLikes > 0)
      activeFilters.push(
        `minRecentAverageLikes: ${minRecentAverageLikes.toLocaleString()}`,
      );

    if (activeFilters.length > 0) {
      console.log(`🔍 Active filters: ${activeFilters.join(', ')}`);
    } else {
      console.log('🔍 No filters applied (all values are 0)');
    }

    if (allKeywords.length === 0) {
      const stepDuration = Date.now() - stepStartTime;
      console.warn('⚠️  No keywords available, returning empty results');
      console.log(
        createStepSeparator(
          'Direct Video Scout',
          'direct-video-scout',
          'END',
          stepDuration,
        ),
      );
      return {
        creators: [],
        videos: [],
        stats: {
          challengeVideosCollected: 0,
          uniqueCreatorsFound: 0,
          creatorsProcessed: 0,
          successfulCreators: 0,
          totalCreatorVideos: 0,
          totalVideosProcessed: 0,
        },
        targetCreatorDescription,
        filterMode,
        pickerMode,
        scoutGuidance,
        creatorRegion,
        desiredCreatorCount,
        concurrentTasksLimit: inputData.concurrentTasksLimit ?? 4,
        uploadToOss: inputData.uploadToOss ?? false,
        downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
        challengeCursorMap: inputData.challengeCursorMap ?? {},
        originalFilters,
        campaignParams,
      };
    }

    // Scout creators from direct video search and combine results
    let totalStats = {
      challengeVideosCollected: 0,
      uniqueCreatorsFound: 0,
      creatorsProcessed: 0,
      successfulCreators: 0,
      totalCreatorVideos: 0,
      totalVideosProcessed: 0,
    };

    // Calculate videos per keyword to reach desired total
    const videosPerKeyword = Math.max(
      Math.ceil((desiredCreatorCount * 2) / allKeywords.length), // Get more videos than creators needed
      SCOUTING_CONFIG.MIN_VIDEO_PER_KEYWORD,
    );

    console.log(
      `🚀 Processing ${allKeywords.length} keywords in parallel (max ${SCOUTING_CONFIG.MAX_CONCURRENT_JOBS} concurrent)`,
    );

    // Create tasks for parallel processing
    const keywordTasks = allKeywords.map((keyword, idx) => async () => {
      // Retry logic for scouting keyword videos
      let retryCount = 0;
      const maxRetries = 3;
      let lastError: any;

      while (retryCount < maxRetries) {
        try {
          if (retryCount > 0) {
            console.log(
              `🔄 Retrying keyword "${keyword}" (attempt ${retryCount + 1}/${maxRetries}) [${idx + 1}/${allKeywords.length}]`,
            );
          }

          // Determine offset strategy based on campaign mode
          let startOffset: number;
          if (campaignMode && sequentialCursorMode) {
            // For campaigns: sequential processing starting from 0
            // Each keyword gets a different starting point to avoid overlap
            startOffset = idx * 20; // Spread keywords across offset positions
            console.log(
              `📊 Campaign mode: Keyword "${keyword}" using sequential offset: ${startOffset} [${idx + 1}/${allKeywords.length}]`,
            );
          } else {
            // For single workflows: random offset for variety
            startOffset = Math.floor(Math.random() * 100); // 0 to 100
            console.log(
              `🎲 Single mode: Keyword "${keyword}" using random offset: ${startOffset} [${idx + 1}/${allKeywords.length}]`,
            );
          }

          // Search for videos using the keyword
          const searchedVideos = await videoScouter.searchTiktokVideos(
            keyword,
            startOffset,
            videosPerKeyword,
            0, // sort_type: 0 = relevance
            0, // publish_time: 0 = unlimited
            creatorRegion, // Pass extracted region for regional video filtering
          );

          console.log(
            `📹 Keyword "${keyword}" found ${searchedVideos.length} videos (offset: ${startOffset}) [${idx + 1}/${allKeywords.length}]`,
          );

          // Filter videos based on criteria and skip lists
          const filteredVideos = searchedVideos.filter((video) => {
            // Skip if video is in campaign skip list
            if (campaignMode && skipVideoIds.includes(video.video_id)) {
              return false;
            }

            // Apply video filters (ignore when value is 0)
            if (minViews > 0 && video.view_count < minViews) return false;
            if (minLikes > 0 && video.like_count < minLikes) return false;
            if (minComments > 0 && video.comment_count < minComments)
              return false;

            return true;
          });

          console.log(
            `✅ Keyword "${keyword}" yielded ${filteredVideos.length} filtered videos [${idx + 1}/${allKeywords.length}]${retryCount > 0 ? ` (succeeded on retry ${retryCount + 1})` : ''}`,
          );

          return {
            keyword,
            idx,
            videos: filteredVideos,
          };
        } catch (error) {
          retryCount++;
          lastError = error;
          console.error(
            `❌ Error scouting keyword "${keyword}" (attempt ${retryCount}/${maxRetries}):`,
            error,
          );

          if (retryCount >= maxRetries) {
            console.error(
              `🚫 Failed to scout keyword "${keyword}" after ${maxRetries} attempts. Skipping keyword.`,
            );
            // Return empty result instead of throwing to allow other keywords to continue
            return {
              keyword,
              idx,
              videos: [],
            };
          } else {
            // Wait before retrying (exponential backoff)
            const waitTime = Math.pow(2, retryCount - 1) * 1000; // 1s, 2s, 4s
            console.log(`⏳ Waiting ${waitTime}ms before retry...`);
            await new Promise((resolve) => setTimeout(resolve, waitTime));
          }
        }
      }

      // This should never be reached, but TypeScript needs a return
      throw lastError;
    });

    // Execute keywords in parallel with controlled concurrency
    const keywordResults: PromiseSettledResult<{
      keyword: string;
      idx: number;
      videos: any[];
    }>[] = [];

    const maxConcurrency = SCOUTING_CONFIG.MAX_CONCURRENT_JOBS;
    for (let i = 0; i < keywordTasks.length; i += maxConcurrency) {
      const batch = keywordTasks.slice(i, i + maxConcurrency);
      const batchResults = await Promise.allSettled(
        batch.map((task) => task()),
      );
      keywordResults.push(...batchResults);

      // Log progress
      console.log(
        `🔄 Processed batch ${Math.floor(i / maxConcurrency) + 1}/${Math.ceil(keywordTasks.length / maxConcurrency)} (${batchResults.length} keywords)`,
      );
    }

    // Collect all videos from keyword searches
    const allSearchedVideos: any[] = [];
    for (const keywordResult of keywordResults) {
      if (keywordResult.status === 'fulfilled') {
        const { videos } = keywordResult.value;
        allSearchedVideos.push(...videos);
        totalStats.challengeVideosCollected += videos.length;
      } else {
        console.error(`Failed keyword processing:`, keywordResult.reason);
      }
    }

    console.log(
      `🎯 Video search complete: ${allSearchedVideos.length} total videos from ${allKeywords.length} keywords`,
    );

    // Extract unique creators from videos and filter out campaign-scouted creators
    const creatorMap = new Map();
    const uniqueCreators = [];

    for (const video of allSearchedVideos) {
      const creator = video.author;
      const creatorId = creator.unique_id;

      // Skip if already processed in this workflow run
      if (creatorMap.has(creatorId)) {
        continue;
      }

      // Skip if already scouted in campaign (campaign-aware deduplication)
      if (campaignMode && skipCreatorIds.includes(creatorId)) {
        continue;
      }

      // Apply creator filters (ignore when value is 0)
      if (minFollowers > 0 && creator.follower_count < minFollowers) {
        continue;
      }

      creatorMap.set(creatorId, creator);
      uniqueCreators.push(creator);
    }

    totalStats.uniqueCreatorsFound = uniqueCreators.length;

    console.log(
      `👥 Found ${uniqueCreators.length} unique creators from ${allSearchedVideos.length} videos`,
    );

    // If we have no creators, return early
    if (uniqueCreators.length === 0) {
      const stepDuration = Date.now() - stepStartTime;
      console.warn('⚠️  No unique creators found, returning empty results');
      console.log(
        createStepSeparator(
          'Direct Video Scout',
          'direct-video-scout',
          'END',
          stepDuration,
        ),
      );
      return {
        creators: [],
        videos: allSearchedVideos,
        stats: totalStats,
        targetCreatorDescription,
        filterMode,
        pickerMode,
        scoutGuidance,
        creatorRegion,
        desiredCreatorCount,
        concurrentTasksLimit: inputData.concurrentTasksLimit ?? 4,
        uploadToOss: inputData.uploadToOss ?? false,
        downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
        challengeCursorMap: inputData.challengeCursorMap ?? {},
        originalFilters,
        campaignParams,
      };
    }

    // Fetch additional posts for each creator to get more comprehensive data
    console.log(
      `📥 Fetching additional posts for ${uniqueCreators.length} creators...`,
    );

    const creatorPostTasks = uniqueCreators.map((creator, idx) => async () => {
      try {
        console.log(
          `📥 Fetching posts for creator: ${creator.unique_id} (${creator.nickname}) [${idx + 1}/${uniqueCreators.length}]`,
        );

        // Use the public searchTiktokVideos method to get creator videos
        // This is a workaround since executeServiceMethod is private
        const videos = await videoScouter.searchTiktokVideos(
          `@${creator.unique_id}`, // Search for videos by this creator
          0, // offset
          SCOUTING_CONFIG.CREATOR_POSTS_PER_FETCH, // count
          0, // sort_type: 0 = relevance
          0, // publish_time: 0 = unlimited
          creatorRegion, // region
        );

        // Filter creator videos based on criteria
        const filteredCreatorVideos = videos.filter((video) => {
          // Apply video filters (ignore when value is 0)
          if (minViews > 0 && video.view_count < minViews) return false;
          if (minLikes > 0 && video.like_count < minLikes) return false;
          if (minComments > 0 && video.comment_count < minComments)
            return false;

          return true;
        });

        // Calculate enhanced metrics for the creator
        const videoMetrics = calculateVideoMetrics(filteredCreatorVideos);

        // Apply advanced creator filters based on video metrics
        if (
          minRecentMedianViews > 0 &&
          videoMetrics.medianViews < minRecentMedianViews
        ) {
          return null; // Filter out this creator
        }
        if (
          minRecentMedianComments > 0 &&
          videoMetrics.medianComments < minRecentMedianComments
        ) {
          return null; // Filter out this creator
        }
        if (
          minRecentMedianLikes > 0 &&
          videoMetrics.medianLikes < minRecentMedianLikes
        ) {
          return null; // Filter out this creator
        }
        if (
          minRecentAverageViews > 0 &&
          videoMetrics.averageViews < minRecentAverageViews
        ) {
          return null; // Filter out this creator
        }
        if (
          minRecentAverageComments > 0 &&
          videoMetrics.averageComments < minRecentAverageComments
        ) {
          return null; // Filter out this creator
        }
        if (
          minRecentAverageLikes > 0 &&
          videoMetrics.averageLikes < minRecentAverageLikes
        ) {
          return null; // Filter out this creator
        }

        console.log(
          `✅ Creator ${creator.unique_id} yielded ${filteredCreatorVideos.length} videos (metrics: ${videoMetrics.averageViews} avg views) [${idx + 1}/${uniqueCreators.length}]`,
        );

        return {
          creator: {
            ...creator,
            // Add enhanced metrics to creator object
            videoCount: filteredCreatorVideos.length,
            ...videoMetrics,
          },
          videos: filteredCreatorVideos,
        };
      } catch (error) {
        console.error(
          `❌ Error fetching posts for creator ${creator.unique_id}:`,
          error,
        );
        return null; // Return null for failed creators
      }
    });

    // Execute creator post fetching in parallel with controlled concurrency
    const creatorPostResults: PromiseSettledResult<{
      creator: any;
      videos: any[];
    } | null>[] = [];

    const creatorConcurrency = Math.min(
      concurrentTasksLimit,
      SCOUTING_CONFIG.MAX_CONCURRENT_JOBS,
    );
    for (let i = 0; i < creatorPostTasks.length; i += creatorConcurrency) {
      const batch = creatorPostTasks.slice(i, i + creatorConcurrency);
      const batchResults = await Promise.allSettled(
        batch.map((task) => task()),
      );
      creatorPostResults.push(...batchResults);

      // Log progress
      console.log(
        `🔄 Processed creator batch ${Math.floor(i / creatorConcurrency) + 1}/${Math.ceil(creatorPostTasks.length / creatorConcurrency)} (${batchResults.length} creators)`,
      );
    }

    // Process results and accumulate data
    const finalCreators: any[] = [];
    const finalVideos: any[] = [];

    for (const creatorResult of creatorPostResults) {
      if (creatorResult.status === 'fulfilled' && creatorResult.value) {
        const { creator, videos } = creatorResult.value;

        finalCreators.push(creator);
        finalVideos.push(...videos);

        // Update stats
        totalStats.creatorsProcessed++;
        totalStats.successfulCreators++;
        totalStats.totalCreatorVideos += videos.length;
        totalStats.totalVideosProcessed += videos.length;
      }
    }

    // Add the original searched videos to the final video collection
    finalVideos.push(...allSearchedVideos);
    totalStats.totalVideosProcessed += allSearchedVideos.length;

    const stepDuration = Date.now() - stepStartTime;
    console.log(
      `✅ Final results: ${finalCreators.length} qualified creators, ${finalVideos.length} total videos`,
    );
    console.log(
      createStepSeparator(
        'Direct Video Scout',
        'direct-video-scout',
        'END',
        stepDuration,
      ),
    );

    return {
      creators: finalCreators,
      videos: finalVideos,
      stats: totalStats,
      targetCreatorDescription,
      filterMode,
      pickerMode,
      scoutGuidance,
      creatorRegion,
      desiredCreatorCount,
      // Pass through image processing parameters
      concurrentTasksLimit: inputData.concurrentTasksLimit ?? 4,
      uploadToOss: inputData.uploadToOss ?? false,
      downloadThumbnailAsBuffer: inputData.downloadThumbnailAsBuffer ?? false,
      challengeCursorMap: inputData.challengeCursorMap ?? {},
      originalFilters,
      campaignParams,
    };
  },
});

// Step 4: Filter creators using the creator filter agent
const filterCreatorsStep = createStep({
  id: 'filter-creators',
  inputSchema: creatorDataSchema.merge(sharedParameterSchema),
  outputSchema: enhancedWorkflowOutputSchema,
  execute: async ({ inputData, mastra, runId }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Filter Creators', 'filter-creators', 'START'),
    );

    const {
      creators,
      videos,
      targetCreatorDescription,
      desiredCreatorCount,
      filterMode,
      scoutGuidance,
      originalFilters,
      uploadToOss,
      downloadThumbnailAsBuffer,
      challengeCursorMap,
    } = inputData;

    // Extract filter parameters from stored original filters
    const {
      minViews,
      minLikes,
      minComments,
      minFollowers,
      minRecentMedianViews,
      minRecentMedianComments,
      minRecentMedianLikes,
      minRecentAverageViews,
      minRecentAverageComments,
      minRecentAverageLikes,
    } = originalFilters;
    console.log(
      `🔍 Filtering ${creators.length} creators in batches (batch size: ${SCOUTING_CONFIG.CREATOR_BATCH_SIZE})`,
    );
    // console.log(
    //   `📹 Using ${videos.length} creator videos for enhanced filtering`,
    // );
    // console.log(`🔧 Filter mode: ${filterMode}`);
    // console.log('🎯 Original requirements:', targetCreatorDescription);
    // console.log(`📊 Target Creator Count: ${desiredCreatorCount}`);

    // Log active filters for reference
    const activeFilters = [];
    if (minViews > 0)
      activeFilters.push(`minViews: ${minViews.toLocaleString()}`);
    if (minLikes > 0)
      activeFilters.push(`minLikes: ${minLikes.toLocaleString()}`);
    if (minComments > 0)
      activeFilters.push(`minComments: ${minComments.toLocaleString()}`);
    if (minFollowers > 0)
      activeFilters.push(`minFollowers: ${minFollowers.toLocaleString()}`);
    if (minRecentMedianViews > 0)
      activeFilters.push(
        `minRecentMedianViews: ${minRecentMedianViews.toLocaleString()}`,
      );
    if (minRecentMedianComments > 0)
      activeFilters.push(
        `minRecentMedianComments: ${minRecentMedianComments.toLocaleString()}`,
      );
    if (minRecentMedianLikes > 0)
      activeFilters.push(
        `minRecentMedianLikes: ${minRecentMedianLikes.toLocaleString()}`,
      );
    if (minRecentAverageViews > 0)
      activeFilters.push(
        `minRecentAverageViews: ${minRecentAverageViews.toLocaleString()}`,
      );
    if (minRecentAverageComments > 0)
      activeFilters.push(
        `minRecentAverageComments: ${minRecentAverageComments.toLocaleString()}`,
      );
    if (minRecentAverageLikes > 0)
      activeFilters.push(
        `minRecentAverageLikes: ${minRecentAverageLikes.toLocaleString()}`,
      );

    if (activeFilters.length > 0) {
      console.log(`📊 Applied filters: ${activeFilters.join(', ')}`);
    }

    const creatorFilterAgent = mastra?.getAgent('creatorFilterAgent');
    if (!creatorFilterAgent) {
      throw new Error('Creator filter agent not found');
    }

    const batchSize = SCOUTING_CONFIG.CREATOR_BATCH_SIZE;
    const allQualifiedCreators: Array<{
      unique_id: string;
      collect_reason: string;
      match_score?: number;
      content_tags?: string[];
      thumbnail_analysis?: string;
    }> = [];

    // Track overall filtering statistics
    let totalAnalyzed = 0;
    let totalQualified = 0;
    const tierCounts = { PERFECT: 0, EXCELLENT: 0, GOOD: 0, ACCEPTABLE: 0 };

    // Create a map of creator videos for enhanced filtering (using essential data only)
    const creatorVideosMap = new Map<string, any[]>();
    for (const video of videos) {
      const creatorId = video.author.unique_id;
      if (!creatorVideosMap.has(creatorId)) {
        creatorVideosMap.set(creatorId, []);
      }
      // Store only essential video data to reduce payload size
      creatorVideosMap.get(creatorId)!.push(extractVideoEssentials(video));
    }

    // Process creators in batches with intelligent stopping logic
    for (let i = 0; i < creators.length; i += batchSize) {
      const batch = creators.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const remainingCreators = creators.length - i - batchSize;

      // console.log(
      //   `Processing batch ${batchNumber} with ${batch.length} creators (${remainingCreators > 0 ? remainingCreators + ' remaining' : 'final batch'})`,
      // );

      try {
        // Enhance creator data with their videos for better filtering
        const enhancedBatch = batch.map((creator) => {
          const creatorVideos = creatorVideosMap.get(creator.unique_id) || [];
          const videoMetrics = calculateVideoMetrics(creatorVideos);

          return {
            ...creator,
            recentVideos: creatorVideos.slice(
              0,
              SCOUTING_CONFIG.MAX_VIDEOS_FOR_CONTEXT,
            ), // Include recent videos for context
            totalVideosAnalyzed: creatorVideos.length,
            // Enhanced metrics for better filtering
            videoMetrics: {
              recentVideosCollected: videoMetrics.recentVideosCollected,
              averageViews: videoMetrics.averageViews,
              averageLikes: videoMetrics.averageLikes,
              averageComments: videoMetrics.averageComments,
              medianViews: videoMetrics.medianViews,
              medianLikes: videoMetrics.medianLikes,
              medianComments: videoMetrics.medianComments,
              avgEngagementRate: videoMetrics.avgEngagementRate,
              totalViews: videoMetrics.totalViews,
              totalLikes: videoMetrics.totalLikes,
              totalComments: videoMetrics.totalComments,
              totalShares: videoMetrics.totalShares,
            },
          };
        });

        // Construct enhanced prompt with filter mode, requirements, and scout guidance
        let prompt = `Filter Mode: ${filterMode}`;

        if (targetCreatorDescription) {
          prompt += `\n\User Specifications: ${targetCreatorDescription}`;
        }

        if (scoutGuidance) {
          prompt += `\n\nExtra Scout Guidance: ${scoutGuidance}`;
        }

        const instructionMessage: CoreMessage = {
          role: 'user',
          content: prompt,
        };

        const userMessages: CoreMessage[] = [];

        try {
          userMessages.push(instructionMessage);

          // Add each creator as a separate user message for individual processing
          for (const batch of enhancedBatch) {
            try {
              // Validate batch data
              if (!batch || !Array.isArray(batch.recentVideos)) {
                console.warn(`⚠️ Invalid batch data for creator, skipping...`);
                continue;
              }

              // Filter and process valid video thumbnails with new image processing options
              const validImageContent = [];

              // Process images based on configuration
              if (downloadThumbnailAsBuffer) {
                // TODO
              } else if (uploadToOss) {
                // TODO
              } else {
                // Use original URLs (existing behavior)
                validImageContent.push(
                  batch.recentVideos
                    .filter(
                      (video: any) =>
                        video &&
                        video.thumbnail_url &&
                        isUrl(video.thumbnail_url),
                    )
                    .map((video: any) => {
                      return {
                        type: 'image',
                        image: new URL(video.thumbnail_url),
                      };
                    }),
                );
              }

              const creatorMessage: CoreUserMessage = {
                role: 'user',
                content: [
                  ...validImageContent,
                  {
                    type: 'text',
                    text: JSON.stringify(batch, null, 2),
                  },
                ],
              };
              userMessages.push(creatorMessage);
            } catch (batchError) {
              console.error(
                `❌ Error processing batch for creator ${batch?.unique_id || 'unknown'}:`,
                batchError,
              );
              // Continue with next batch instead of failing entire step
              continue;
            }
          }

          // Validate that we have at least the instruction message
          if (userMessages.length <= 1) {
            throw new Error(
              `No valid creator batches could be processed for batch ${batchNumber}`,
            );
          }
        } catch (messageConstructionError) {
          console.error(
            `❌ Error constructing user messages for batch ${batchNumber}:`,
            messageConstructionError,
          );
          // Skip this entire batch and continue with next one
          continue;
        }

        // Save userMessages to a local JSON file for debugging/analysis
        // const messagesJSON = JSON.stringify(userMessages, null, 2);
        // const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        // const filename = `creator-filter-messages-batch-${batchNumber}-${timestamp}.json`;
        // const filepath = `./debug/${filename}`;

        // try {
        //   // Ensure debug directory exists
        //   const { mkdirSync, writeFileSync } = await import('fs');
        //   mkdirSync('./debug', { recursive: true });

        //   writeFileSync(filepath, messagesJSON, 'utf8');
        //   console.log(`💾 Saved batch ${batchNumber} messages to: ${filepath}`);
        // } catch (error) {
        //   console.warn(
        //     `⚠️ Failed to save messages to file: ${error instanceof Error ? error.message : String(error)}`,
        //   );
        // }

        // Retry logic for agent call (up to 3 attempts)
        let retryCount = 0;
        const maxRetries = 3;
        let batchProcessed = false;

        while (retryCount < maxRetries && !batchProcessed) {
          try {
            if (retryCount > 0) {
              console.log(
                `🔄 Retrying batch ${batchNumber} (attempt ${retryCount + 1}/${maxRetries})`,
              );
            }

            const resp = await creatorFilterAgent.generate(userMessages, {
              output: CreatorFilterOutputSchema,
            });

            const object = resp.object;
            let batchAvgMatchScore = 0;

            // Filter creators by match score based on filter mode
            const minScore = filterMode === 'STRICT' ? 0.85 : 0.65;
            const scoreFilteredCreators = object.qualified_kols.filter(
              (creator) => {
                const score = creator.match_score || 0;
                return score >= minScore;
              },
            );

            // Log filtering results
            const originalCount = object.qualified_kols.length;
            const filteredCount = scoreFilteredCreators.length;
            if (originalCount > filteredCount) {
              console.log(
                `🎯 Score filtering (${filterMode} mode, min: ${minScore}): ${originalCount} → ${filteredCount} creators (${originalCount - filteredCount} filtered out)`,
              );
            }

            // Update statistics (use filtered count)
            totalAnalyzed += userMessages.length - 1;
            totalQualified += filteredCount;

            // Calculate running average of match scores (use filtered creators)
            const batchMatchScores = scoreFilteredCreators
              .map((creator) => creator.match_score)
              .filter((score) => score !== undefined) as number[];

            if (batchMatchScores.length > 0) {
              batchAvgMatchScore =
                batchMatchScores.reduce((sum, score) => sum + score, 0) /
                batchMatchScores.length;
            }

            // Update tier counts (use filtered creators)
            tierCounts.PERFECT += scoreFilteredCreators.filter(
              (creator) => creator.match_score >= 0.95,
            ).length;
            tierCounts.EXCELLENT += scoreFilteredCreators.filter(
              (creator) =>
                creator.match_score >= 0.85 && creator.match_score < 0.95,
            ).length;
            tierCounts.GOOD += scoreFilteredCreators.filter(
              (creator) =>
                creator.match_score >= 0.75 && creator.match_score < 0.85,
            ).length;
            tierCounts.ACCEPTABLE += scoreFilteredCreators.filter(
              (creator) =>
                creator.match_score >= 0.65 && creator.match_score < 0.75,
            ).length;

            // Add qualified creators with enhanced data
            const enhancedCreators = scoreFilteredCreators.map((creator) => ({
              unique_id: creator.unique_id,
              collect_reason: creator.collect_reason,
              content_tags: creator.content_tags,
              match_score: creator.match_score,
              thumbnail_analysis: creator.thumbnail_analysis,
            }));

            allQualifiedCreators.push(...enhancedCreators);

            console.log(
              `Batch ${batchNumber} yielded ${filteredCount} qualified creators (Mode: ${object.mode}, Avg Score: ${batchAvgMatchScore.toFixed(2)})${retryCount > 0 ? ` (succeeded on retry ${retryCount + 1})` : ''}`,
            );

            // Mark batch as successfully processed
            batchProcessed = true;

            // Intelligent stopping logic
            const currentPerfectCount = tierCounts.PERFECT;
            const currentTotalQualified = allQualifiedCreators.length;

            // console.log(
            //   `Current status: ${currentTotalQualified} total qualified, ${currentPerfectCount} PERFECT tier (target: ${desiredCreatorCount})`,
            // );

            // Early termination condition 1: We have enough PERFECT tier creators
            if (currentPerfectCount >= desiredCreatorCount) {
              console.log(
                `✅ Found enough PERFECT tier creators (${currentPerfectCount} >= ${desiredCreatorCount}). Stopping early.`,
              );
              break;
            }

            // Early termination condition 2: We have enough total creators and no more batches to process
            if (
              currentTotalQualified >= desiredCreatorCount &&
              remainingCreators <= 0
            ) {
              console.log(
                `✅ Found enough total qualified creators (${currentTotalQualified} >= ${desiredCreatorCount}) and no more creators to process.`,
              );
              break;
            }

            // Continue processing condition: We have enough total but want more PERFECT tier
            if (
              currentTotalQualified >= desiredCreatorCount &&
              remainingCreators > 0
            ) {
              // console.log(
              //   `🔄 Have enough total creators (${currentTotalQualified}) but only ${currentPerfectCount} PERFECT tier. Continuing to find more PERFECT matches...`,
              // );
            }
          } catch (error) {
            retryCount++;
            console.error(
              `❌ Error processing batch ${batchNumber} (attempt ${retryCount}/${maxRetries}):`,
              error,
            );

            if (retryCount >= maxRetries) {
              console.error(
                `🚫 Failed to process batch ${batchNumber} after ${maxRetries} attempts. Skipping batch.`,
              );
            } else {
              // Wait a bit before retrying (exponential backoff)
              const waitTime = Math.pow(2, retryCount - 1) * 1000; // 1s, 2s, 4s
              console.log(`⏳ Waiting ${waitTime}ms before retry...`);
              await new Promise((resolve) => setTimeout(resolve, waitTime));
            }
          }
        }
      } catch (error) {
        console.error(`Error processing batch ${batchNumber}:`, error);
        // Continue with next batch
      }
    }

    // console.log(
    //   `Total qualified creators found: ${allQualifiedCreators.length}`,
    // );

    // Sort creators by match_score in descending order (highest scores first)
    const sortedCreators = allQualifiedCreators.sort((a, b) => {
      // Sort by match_score descending, then by tier priority (PERFECT > EXCELLENT > GOOD > ACCEPTABLE)
      const scoreA = a.match_score || 0;
      const scoreB = b.match_score || 0;

      return scoreB - scoreA;
    });

    // Create a map to lookup original creator data and video metrics
    const creatorDataMap = new Map();
    for (const creator of creators) {
      creatorDataMap.set(creator.unique_id, creator);
    }

    // Convert to the expected output format with enhanced data
    const results = sortedCreators.map((creator) => {
      // Get original creator data
      const originalCreator = creatorDataMap.get(creator.unique_id);

      // Get creator videos for metrics calculation
      const creatorVideos = creatorVideosMap.get(creator.unique_id) || [];
      const videoMetrics = calculateVideoMetrics(creatorVideos);

      // Determine tier based on match_score
      let tier:
        | 'PERFECT'
        | 'EXCELLENT'
        | 'GOOD'
        | 'ACCEPTABLE'
        | 'INVALID'
        | undefined;
      if (creator.match_score !== undefined) {
        if (creator.match_score >= 0.95) tier = 'PERFECT';
        else if (creator.match_score >= 0.85) tier = 'EXCELLENT';
        else if (creator.match_score >= 0.75) tier = 'GOOD';
        else if (creator.match_score >= 0.65) tier = 'ACCEPTABLE';
        else tier = 'INVALID';
      }

      return {
        url: `https://www.tiktok.com/@${creator.unique_id}`,
        reason: creator.collect_reason,
        match_score: creator.match_score,
        content_tags: creator.content_tags,
        tier,
        creatorMetrics: originalCreator
          ? {
              // Standard creator metrics
              unique_id: originalCreator.unique_id,
              nickname: originalCreator.nickname,
              follower_count: originalCreator.follower_count,
              aweme_count: originalCreator.aweme_count,
              region: originalCreator.region,
              language: originalCreator.language,

              // Connect metrics
              signature: originalCreator.signature,
              youtube_channel_id: originalCreator.youtube_channel_id,
              ins_id: originalCreator.ins_id,
              twitter_id: originalCreator.twitter_id,

              // Advanced video metrics
              recentVideosCollected: videoMetrics.recentVideosCollected,
              averageViews: videoMetrics.averageViews,
              averageLikes: videoMetrics.averageLikes,
              averageComments: videoMetrics.averageComments,
              medianViews: videoMetrics.medianViews,
              medianLikes: videoMetrics.medianLikes,
              medianComments: videoMetrics.medianComments,
              avgEngagementRate: videoMetrics.avgEngagementRate,
            }
          : undefined,
      };
    });

    // Enhanced logging for final results
    const perfectCount = tierCounts.PERFECT;
    const totalFound = allQualifiedCreators.length;

    console.log(`\n📊 FINAL FILTERING RESULTS:`);
    console.log(`Target: ${desiredCreatorCount} creators`);
    console.log(`Found: ${totalFound} total qualified creators`);
    console.log(`PERFECT tier: ${perfectCount} creators`);
    console.log(
      `Results exceed target: ${totalFound > desiredCreatorCount ? 'Yes' : 'No'}`,
    );
    console.log(
      `Enough PERFECT tier: ${perfectCount >= desiredCreatorCount ? 'Yes' : 'No'}`,
    );

    // Log sample of enhanced metrics for top creators
    if (results.length > 0) {
      console.log(`\n🏆 TOP CREATOR METRICS SAMPLE:`);
      const topCreators = results.slice(0, 3); // Show top 3
      topCreators.forEach((creator, idx) => {
        const metrics = creator.creatorMetrics;
        if (metrics) {
          console.log(
            `\n${idx + 1}. @${metrics.unique_id} (${creator.tier || 'N/A'} tier, score: ${creator.match_score?.toFixed(2) || 'N/A'})`,
          );
          console.log(
            `   👥 Followers: ${metrics.follower_count?.toLocaleString() || 'N/A'} | Posts: ${metrics.aweme_count || 'N/A'}`,
          );
          console.log(
            `   📹 Recent Videos: ${metrics.recentVideosCollected} | Median Views: ${metrics.medianViews.toLocaleString()}`,
          );
          console.log(
            `   💖 Median Likes: ${metrics.medianLikes.toLocaleString()} | Engagement: ${metrics.avgEngagementRate.toFixed(2)}%`,
          );
          console.log(
            `   🌍 Region: ${metrics.region || 'N/A'} | Language: ${metrics.language || 'N/A'}`,
          );
        }
      });
    }

    // console.log(
    //   'Enhanced results with scores and tiers (sorted by match_score):',
    //   results.slice(0, 5),
    // ); // Show top 5
    // console.log(
    //   `Filter Summary - Mode: ${filterMode}, Avg Score: ${avgMatchScore.toFixed(2)}, Tier Distribution:`,
    //   tierCounts,
    // );

    // Calculate step and total workflow duration
    const stepDuration = Date.now() - stepStartTime;
    const totalWorkflowDuration = Date.now() - workflowStartTime;

    console.log(
      createStepSeparator(
        'Filter Creators',
        'filter-creators',
        'END',
        stepDuration,
      ),
    );

    // Final workflow summary
    const workflowSeparator = '🎉'.repeat(20);
    console.log(`\n${workflowSeparator}`);
    console.log(`🏁 WORKFLOW COMPLETED SUCCESSFULLY!`);
    console.log(`⏱️ Total Duration: ${formatDuration(totalWorkflowDuration)}`);
    console.log(`📊 Final Results: ${totalFound} qualified creators found`);
    console.log(`🏆 PERFECT Tier: ${perfectCount} creators`);
    console.log(
      `🎯 Target Met: ${totalFound >= desiredCreatorCount ? '✅ YES' : '❌ NO'}`,
    );
    console.log(`${workflowSeparator}\n`);

    // Save full results to database context with error handling
    let contextId: number | null = null;
    try {
      const fullResultsData = {
        scoutedCreators: creators.length,
        qualifiedCreators: allQualifiedCreators.length,
        results,
        filterSummary: {
          mode: filterMode,
          total_analyzed: totalAnalyzed,
          total_qualified: totalQualified,
          tier_breakdown: tierCounts,
        },
        // Include additional context data with safe mapping
        creators: creators.map((creator) => ({
          unique_id: creator?.unique_id || 'unknown',
          nickname: creator?.nickname || 'unknown',
          follower_count: creator?.follower_count || 0,
          aweme_count: creator?.aweme_count || 0,
        })),
        videos: videos.map((video) => ({
          video_id: video?.video_id || 'unknown',
          author_id: video?.author?.unique_id || 'unknown',
          view_count: video?.view_count || 0,
          like_count: video?.like_count || 0,
          comment_count: video?.comment_count || 0,
        })),
        timestamp: new Date().toISOString(),
        // Include challenge cursor map for campaign progression tracking
        challengeCursorMap: challengeCursorMap || {},
      };

      contextId = await workflowDbService.saveWorkflowContext(
        runId,
        'creator_scout_results',
        fullResultsData,
      );
      console.log(
        `✅ Successfully saved workflow context with ID: ${contextId}`,
      );
    } catch (dbError) {
      console.error(`❌ Failed to save workflow context to database:`, dbError);
      // Continue execution even if database save fails
    }

    // Return only essential data with context ID (or fallback if save failed)
    return {
      scoutedCreators: creators.length,
      qualifiedCreators: allQualifiedCreators.length,
      contextId: contextId ? contextId.toString() : 'db-save-failed',
      filterSummary: {
        mode: filterMode,
        total_analyzed: totalAnalyzed,
        total_qualified: totalQualified,
        tier_breakdown: tierCounts,
      },
    };
  },
});

// Define the workflow steps and their relationships
creatorScoutWorkflow
  .then(analyzeRequirementStep)
  .then(selectChallengesStep)
  .then(scoutCreatorsStep)
  .then(filterCreatorsStep)
  .commit();

creatorDirectScoutWorkflow
  .then(analyzeRequirementStep)
  .then(directVideoScoutStep)
  .then(filterCreatorsStep)
  .commit();

/**
 * Helper function to retrieve full creator scout results from database
 * @param runId The workflow run ID
 * @returns Full creator scout results with all context data
 */
export async function getCreatorScoutResults(runId: string) {
  const { workflowDbService } = await import('@/services/index');

  // Get all context data for this workflow run
  const [hashtagAnalysis, selectedChallenges, scoutedCreators, finalResults] =
    await Promise.all([
      workflowDbService.getWorkflowContext(runId, 'hashtag_analysis'),
      workflowDbService.getWorkflowContext(runId, 'selected_challenges'),
      workflowDbService.getWorkflowContext(runId, 'scouted_creators'),
      workflowDbService.getWorkflowContext(runId, 'creator_scout_results'),
    ]);

  return {
    hashtagAnalysis: hashtagAnalysis?.contextData,
    selectedChallenges: selectedChallenges?.contextData,
    scoutedCreators: scoutedCreators?.contextData,
    finalResults: finalResults?.contextData,
  };
}

/**
 * Helper function to retrieve only the final filtered results
 * @param contextId The context ID from the workflow output
 * @returns Final creator scout results
 */
export async function getCreatorScoutResultsByContextId(contextId: string) {
  const { workflowDbService } = await import('@/services/index');

  try {
    // Convert contextId to number and get the context by ID
    const contextIdNum = parseInt(contextId, 10);
    if (isNaN(contextIdNum)) {
      console.error(`❌ Invalid contextId format: ${contextId}`);
      return null;
    }

    const context = await workflowDbService.getContextById(contextIdNum);

    if (!context) {
      console.error(`❌ No context found for contextId: ${contextId}`);
      return null;
    }

    // Verify this is the correct context type
    if (context.contextType !== 'creator_scout_results') {
      console.error(
        `❌ Unexpected context type: ${context.contextType}, expected: creator_scout_results`,
      );
      return null;
    }

    return context.contextData;
  } catch (error) {
    console.error(`❌ Error fetching context by ID ${contextId}:`, error);
    return null;
  }
}
